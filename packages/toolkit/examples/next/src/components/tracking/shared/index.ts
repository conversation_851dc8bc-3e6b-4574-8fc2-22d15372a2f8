/**
 * Shared Tracking Components and Utilities
 * 
 * This module exports unified components and utilities for tracking analytics,
 * providing a consistent interface across heatmap and session replay components.
 */

// Main filter component and types
export {
	TrackingFilter,
	createInitialFilterData,
	type ITrackingFilterData,
	type ITrackingFilterProps,
	type IFilterValidation
} from './tracking-filter';

// Filter hook and utilities
export {
	useTrackingFilter,
	validateFilterData,
	formatFilterSummary,
	type IUseTrackingFilterConfig,
	type IUseTrackingFilterReturn
} from './use-tracking-filter';

// Example components for reference
export {
	TrackingFilterExample,
	MinimalFilterExample,
	AdvancedFilterExample
} from './tracking-filter-example';

/**
 * Re-export commonly used types from other modules for convenience
 */
export type { IClocSession } from '../types/api-types';
export { isSuccessResponse } from '../types/api-types';
export { trackingApiClient } from '../api-client/tracking-client';
