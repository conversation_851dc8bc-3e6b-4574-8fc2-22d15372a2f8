'use client';

import React from 'react';
import { cn } from '@cloc/ui';
import { TrackingFilter, ITrackingFilterData } from './tracking-filter';
import { useTrackingFilter, formatFilterSummary } from './use-tracking-filter';

/**
 * Example component demonstrating the unified tracking filter usage
 * 
 * This component shows how to integrate the TrackingFilter component
 * with the useTrackingFilter hook for a complete filtering solution.
 */
export const TrackingFilterExample: React.FC<{ className?: string }> = ({ className }) => {
	// Use the tracking filter hook with configuration
	const {
		filterData,
		setFilterData,
		sessions,
		loading,
		error,
		fetchSessions,
		clearError,
		resetFilter,
		isContextReady
	} = useTrackingFilter({
		autoFetch: true,
		onError: (error) => {
			console.error('Filter error:', error);
		},
		onSuccess: (sessions) => {
			console.log('Filter success:', sessions.length, 'sessions found');
		}
	});

	// Custom validation example
	const customValidation = (filterData: ITrackingFilterData) => {
		const errors: string[] = [];
		const fromDate = new Date(filterData.from);
		const toDate = new Date(filterData.to);
		
		// Example: Limit to maximum 7 days range
		const maxDays = 7;
		const daysDiff = (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24);
		
		if (daysDiff > maxDays) {
			errors.push(`Date range cannot exceed ${maxDays} days`);
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	};

	return (
		<div className={cn('rounded-xl shadow-lg bg-white dark:bg-black px-6 py-4 w-full', className)}>
			<h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
				Unified Tracking Filter Example
			</h1>

			{/* Context Status */}
			<div className="mb-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-900">
				<div className="text-sm">
					<strong>Context Status:</strong>{' '}
					<span className={isContextReady ? 'text-green-600' : 'text-red-600'}>
						{isContextReady ? 'Ready' : 'Not Ready'}
					</span>
				</div>
				{filterData && (
					<div className="text-sm mt-1">
						<strong>Current Filter:</strong> {formatFilterSummary(filterData)}
					</div>
				)}
			</div>

			{/* Unified Filter Component */}
			<TrackingFilter
				filterData={filterData}
				onFilterChange={setFilterData}
				onApplyFilter={fetchSessions}
				loading={loading}
				defaultExpanded={true}
				customValidation={customValidation}
			/>

			{/* Filter Actions */}
			<div className="flex gap-2 mb-4">
				<button
					onClick={resetFilter}
					className="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
				>
					Reset Filter
				</button>
				{error && (
					<button
						onClick={clearError}
						className="px-3 py-1 text-sm bg-red-200 dark:bg-red-700 rounded hover:bg-red-300 dark:hover:bg-red-600"
					>
						Clear Error
					</button>
				)}
			</div>

			{/* Error Display */}
			{error && (
				<div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
					<div className="text-red-800 dark:text-red-200 text-sm">
						<strong>Error:</strong> {error}
					</div>
				</div>
			)}

			{/* Results Display */}
			<div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
				<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
					Filter Results
				</h3>
				
				{loading && (
					<div className="text-center py-8">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
						<p className="mt-2 text-gray-600 dark:text-gray-400">Loading sessions...</p>
					</div>
				)}

				{!loading && !error && sessions.length === 0 && (
					<div className="text-center py-8">
						<p className="text-gray-600 dark:text-gray-400">No sessions found for the current filter.</p>
					</div>
				)}

				{!loading && sessions.length > 0 && (
					<div>
						<p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
							Found {sessions.length} session(s)
						</p>
						<div className="space-y-2 max-h-64 overflow-y-auto">
							{sessions.map((session) => (
								<div
									key={session.sessionId}
									className="p-3 bg-gray-50 dark:bg-gray-900 rounded border"
								>
									<div className="text-sm">
										<div className="font-mono text-xs text-gray-500">
											ID: {session.sessionId}
										</div>
										<div className="text-gray-700 dark:text-gray-300">
											Employee: {session.employeeId}
										</div>
										<div className="text-gray-700 dark:text-gray-300">
											Payloads: {session.payloads.length}
										</div>
										<div className="text-gray-500 text-xs">
											Created: {new Date(session.createdAt).toLocaleString()}
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

/**
 * Minimal usage example for quick integration
 */
export const MinimalFilterExample: React.FC = () => {
	const { filterData, setFilterData, fetchSessions, loading } = useTrackingFilter();

	return (
		<TrackingFilter
			filterData={filterData}
			onFilterChange={setFilterData}
			onApplyFilter={fetchSessions}
			loading={loading}
		/>
	);
};

/**
 * Advanced usage example with custom configuration
 */
export const AdvancedFilterExample: React.FC = () => {
	const {
		filterData,
		setFilterData,
		sessions,
		loading,
		error,
		fetchSessions,
		resetFilter
	} = useTrackingFilter({
		autoFetch: false, // Manual fetch only
		onError: (error) => {
			// Custom error handling
			console.error('Custom error handler:', error);
		},
		onSuccess: (sessions) => {
			// Custom success handling
			console.log('Custom success handler:', sessions);
		}
	});

	return (
		<div className="space-y-4">
			<TrackingFilter
				filterData={filterData}
				onFilterChange={setFilterData}
				onApplyFilter={fetchSessions}
				loading={loading}
				defaultExpanded={true}
			/>
			
			{/* Custom controls */}
			<div className="flex gap-2">
				<button onClick={fetchSessions} disabled={loading}>
					Manual Fetch
				</button>
				<button onClick={resetFilter}>
					Reset
				</button>
			</div>

			{/* Results summary */}
			{sessions.length > 0 && (
				<div className="text-sm text-gray-600">
					{sessions.length} sessions loaded
				</div>
			)}
		</div>
	);
};

export default TrackingFilterExample;
