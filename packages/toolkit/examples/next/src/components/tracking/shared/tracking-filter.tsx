'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
	Accordion,
	AccordionItem,
	AccordionTrigger,
	AccordionContent,
	Input,
	ThemedButton,
	DatePicker,
	cn
} from '@cloc/ui';
import { Calendar, ListFilter, Loader2, TimerIcon } from 'lucide-react';
import { useClocContext } from '@cloc/atoms';

/**
 * Unified Filter Data Structure
 * Represents all filtering parameters used across tracking components
 */
export interface ITrackingFilterData {
	from: string; // ISO 8601 date string
	to: string; // ISO 8601 date string
	employeeIds: string[] | null; // null for 'all', array for specific employees
}

/**
 * Filter validation result
 */
export interface IFilterValidation {
	isValid: boolean;
	errors: string[];
}

/**
 * Props for the TrackingFilter component
 */
export interface ITrackingFilterProps {
	/** Current filter data */
	filterData: ITrackingFilterData;
	/** Callback when filter data changes */
	onFilterChange: (filterData: ITrackingFilterData) => void;
	/** Callback when apply filter is clicked */
	onApplyFilter: () => void;
	/** Loading state for apply button */
	loading?: boolean;
	/** Custom CSS class */
	className?: string;
	/** Whether to show the filter in expanded state by default */
	defaultExpanded?: boolean;
	/** Custom validation function */
	customValidation?: (filterData: ITrackingFilterData) => IFilterValidation;
}

/**
 * Custom hook for filter validation
 */
const useFilterValidation = (
	filterData: ITrackingFilterData,
	customValidation?: (filterData: ITrackingFilterData) => IFilterValidation
): IFilterValidation => {
	return useMemo(() => {
		const errors: string[] = [];

		// Validate date format
		const fromDate = new Date(filterData.from);
		const toDate = new Date(filterData.to);

		if (isNaN(fromDate.getTime())) {
			errors.push('Invalid from date format. Please use ISO 8601 format.');
		}

		if (isNaN(toDate.getTime())) {
			errors.push('Invalid to date format. Please use ISO 8601 format.');
		}

		// Validate date range
		if (!isNaN(fromDate.getTime()) && !isNaN(toDate.getTime()) && fromDate >= toDate) {
			errors.push('From date must be earlier than to date.');
		}

		// Apply custom validation if provided
		if (customValidation) {
			const customResult = customValidation(filterData);
			if (!customResult.isValid) {
				errors.push(...customResult.errors);
			}
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	}, [filterData, customValidation]);
};

/**
 * Custom hook to sync filter data with context changes
 */
const useContextSync = (
	selectedEmployee: string,
	onFilterChange: (filterData: ITrackingFilterData) => void,
	currentFilterData: ITrackingFilterData
) => {
	useEffect(() => {
		if (selectedEmployee) {
			const newEmployeeIds = selectedEmployee === 'all' ? null : [selectedEmployee];
			
			// Only update if the employee selection actually changed
			if (JSON.stringify(currentFilterData.employeeIds) !== JSON.stringify(newEmployeeIds)) {
				onFilterChange({
					...currentFilterData,
					employeeIds: newEmployeeIds
				});
			}
		}
	}, [selectedEmployee, onFilterChange, currentFilterData]);
};

/**
 * TrackingFilter - Unified filter component for tracking analytics
 * 
 * This component provides a consistent filtering interface for both heatmap and session replay components.
 * It handles date/time range selection, employee filtering, and validation.
 * 
 * Features:
 * - Date range selection with DatePicker
 * - Time range inputs for precise timing
 * - Employee filtering via context integration
 * - Built-in validation with custom validation support
 * - Loading states and error handling
 * - Accordion-based collapsible UI
 * - Full TypeScript support
 * 
 * @param filterData - Current filter state
 * @param onFilterChange - Callback when filter data changes
 * @param onApplyFilter - Callback when apply button is clicked
 * @param loading - Loading state for apply button
 * @param className - Custom CSS classes
 * @param defaultExpanded - Whether filter should be expanded by default
 * @param customValidation - Optional custom validation function
 */
export const TrackingFilter: React.FC<ITrackingFilterProps> = ({
	filterData,
	onFilterChange,
	onApplyFilter,
	loading = false,
	className,
	defaultExpanded = false,
	customValidation
}) => {
	const { selectedEmployee } = useClocContext();
	const validation = useFilterValidation(filterData, customValidation);

	// Sync filter data with context changes
	useContextSync(selectedEmployee, onFilterChange, filterData);

	// Handle date change from DatePicker
	const handleDateChange = useCallback((newDate: Date | undefined) => {
		if (!newDate) return;
		
		const date = newDate as Date;
		const newFromDate = `${date.toISOString().split('T')[0]}T${filterData.from.split('T')[1]}`;
		
		onFilterChange({
			...filterData,
			from: newFromDate
		});
	}, [filterData, onFilterChange]);

	// Handle time changes
	const handleFromTimeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
		const newFromDate = `${filterData.from.split('T')[0]}T${e.target.value}:00.000Z`;
		onFilterChange({
			...filterData,
			from: newFromDate
		});
	}, [filterData, onFilterChange]);

	const handleToTimeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
		const newToDate = `${filterData.to.split('T')[0]}T${e.target.value}:00.000Z`;
		onFilterChange({
			...filterData,
			to: newToDate
		});
	}, [filterData, onFilterChange]);

	// Handle apply filter with validation
	const handleApplyFilter = useCallback(() => {
		if (validation.isValid) {
			onApplyFilter();
		}
	}, [validation.isValid, onApplyFilter]);

	return (
		<div className={cn('mb-4 rounded-lg border border-gray-200 dark:border-gray-700 p-3', className)}>
			<Accordion className="w-full text-sm mb-4 rounded-lg" type="multiple" defaultValue={defaultExpanded ? ['filter'] : []}>
				<AccordionItem value="filter">
					<AccordionTrigger className="py-0">
						<div className="flex gap-2 justify-center items-center font-semibold text-gray-900 dark:text-white">
							<ListFilter size={15} className="text-gray-400" />
							<h2>Filters</h2>
						</div>
					</AccordionTrigger>
					<AccordionContent className="p-0 w-full">
						<div className="flex flex-col gap-4 mt-4">
							{/* Validation Errors */}
							{!validation.isValid && (
								<div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
									<div className="text-red-800 dark:text-red-200 text-sm">
										<strong>Validation Errors:</strong>
										<ul className="mt-1 list-disc list-inside">
											{validation.errors.map((error, index) => (
												<li key={index}>{error}</li>
											))}
										</ul>
									</div>
								</div>
							)}

							{/* Date and Time Controls */}
							<div className="flex flex-col gap-4">
								<div className="w-full">
									<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										<Calendar className="inline h-4 w-4 mr-1" />
										Date
									</label>
									<DatePicker
										date={new Date(filterData.from.split('T')[0])}
										setDate={handleDateChange}
										className="w-full"
									/>
								</div>
								
								<div className="w-full">
									<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										<TimerIcon className="inline h-4 w-4 mr-1" />
										Time Range
									</label>
									<div className="flex w-full gap-2">
										<Input
											type="time"
											value={filterData.from.split('T')[1].slice(0, 5)}
											onChange={handleFromTimeChange}
											className="w-full"
											placeholder="From time"
										/>
										<Input
											type="time"
											value={filterData.to.split('T')[1].slice(0, 5)}
											onChange={handleToTimeChange}
											className="w-full"
											placeholder="To time"
										/>
									</div>
								</div>
							</div>

							{/* Apply Filter Button */}
							<ThemedButton 
								onClick={handleApplyFilter} 
								disabled={loading || !validation.isValid}
								className="w-full"
							>
								{loading && <Loader2 className="animate-spin h-4 w-4 mr-2" />}
								Apply Filter
							</ThemedButton>
						</div>
					</AccordionContent>
				</AccordionItem>
			</Accordion>
		</div>
	);
};

/**
 * Utility function to create initial filter data
 */
export const createInitialFilterData = (selectedEmployee?: string): ITrackingFilterData => {
	const fromDate = new Date();
	const toDate = new Date(fromDate.getTime() + 60 * 60 * 1000); // Add 1 hour

	return {
		from: fromDate.toISOString(),
		to: toDate.toISOString(),
		employeeIds: selectedEmployee === 'all' ? null : selectedEmployee ? [selectedEmployee] : null
	};
};

export default TrackingFilter;
