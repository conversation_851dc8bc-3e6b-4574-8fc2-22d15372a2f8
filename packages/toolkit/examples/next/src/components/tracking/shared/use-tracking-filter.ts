import { useState, useEffect, useCallback, useMemo } from 'react';
import { useClocContext } from '@cloc/atoms';
import { trackingApiClient } from '../api-client/tracking-client';
import { IClocSession, isSuccessResponse } from '../types/api-types';
import { ITrackingFilterData, createInitialFilterData } from './tracking-filter';

/**
 * Configuration options for the tracking filter hook
 */
export interface IUseTrackingFilterConfig {
	/** Whether to auto-fetch when context changes */
	autoFetch?: boolean;
	/** Custom initial filter data */
	initialFilterData?: ITrackingFilterData;
	/** Custom error handler */
	onError?: (error: string) => void;
	/** Custom success handler */
	onSuccess?: (sessions: IClocSession[]) => void;
}

/**
 * Return type for the tracking filter hook
 */
export interface IUseTrackingFilterReturn {
	/** Current filter data */
	filterData: ITrackingFilterData;
	/** Update filter data */
	setFilterData: (filterData: ITrackingFilterData) => void;
	/** Fetched sessions */
	sessions: IClocSession[];
	/** Loading state */
	loading: boolean;
	/** Error state */
	error: string | null;
	/** Manually trigger fetch */
	fetchSessions: () => Promise<void>;
	/** Clear error */
	clearError: () => void;
	/** Reset filter to initial state */
	resetFilter: () => void;
	/** Check if context is ready for API calls */
	isContextReady: boolean;
}

/**
 * Custom hook for managing tracking filter state and API integration
 * 
 * This hook provides a complete solution for filter state management,
 * API integration, and context synchronization for tracking components.
 * 
 * Features:
 * - Automatic context synchronization
 * - Built-in API integration with error handling
 * - Loading states and error management
 * - Auto-fetch capabilities
 * - Filter reset functionality
 * - TypeScript support
 * 
 * @param config - Configuration options for the hook
 * @returns Object containing filter state, sessions data, and control functions
 */
export const useTrackingFilter = (config: IUseTrackingFilterConfig = {}): IUseTrackingFilterReturn => {
	const {
		autoFetch = true,
		initialFilterData,
		onError,
		onSuccess
	} = config;

	// Context dependencies
	const { selectedOrganization, token, selectedEmployee, authenticatedUser: user } = useClocContext();

	// State management
	const [filterData, setFilterData] = useState<ITrackingFilterData>(() => 
		initialFilterData || createInitialFilterData(selectedEmployee)
	);
	const [sessions, setSessions] = useState<IClocSession[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Check if context is ready for API calls
	const isContextReady = useMemo(() => {
		return !!(selectedOrganization && selectedEmployee && token && user);
	}, [selectedOrganization, selectedEmployee, token, user]);

	// Clear error function
	const clearError = useCallback(() => {
		setError(null);
	}, []);

	// Reset filter function
	const resetFilter = useCallback(() => {
		setFilterData(createInitialFilterData(selectedEmployee));
		setSessions([]);
		setError(null);
	}, [selectedEmployee]);

	// Main fetch function
	const fetchSessions = useCallback(async () => {
		if (!isContextReady) {
			const errorMsg = 'Missing required context: organization, employee, or token';
			setError(errorMsg);
			onError?.(errorMsg);
			return;
		}

		setLoading(true);
		setError(null);

		try {
			// Validate date format
			const fromDate = new Date(filterData.from);
			const toDate = new Date(filterData.to);

			if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
				throw new Error('Invalid date format. Please use ISO 8601 format (e.g., 2024-01-01T00:00:00.000Z)');
			}

			if (fromDate >= toDate) {
				throw new Error('From date must be earlier than to date');
			}

			// Call the filtered sessions endpoint
			const response = await trackingApiClient.getFilteredSessions(
				filterData.from,
				filterData.to,
				filterData.employeeIds,
				selectedOrganization!,
				user!.tenantId,
				token!
			);

			if (isSuccessResponse(response)) {
				setSessions(response.data);
				setError(null);
				onSuccess?.(response.data);
			} else {
				const errorMsg = response.error || 'Failed to fetch sessions';
				setError(errorMsg);
				setSessions([]);
				onError?.(errorMsg);
			}
		} catch (err) {
			const errorMsg = err instanceof Error ? err.message : 'An unexpected error occurred';
			setError(errorMsg);
			setSessions([]);
			onError?.(errorMsg);
		} finally {
			setLoading(false);
		}
	}, [
		isContextReady,
		filterData,
		selectedOrganization,
		user,
		token,
		onError,
		onSuccess
	]);

	// Auto-fetch when context changes (if enabled)
	useEffect(() => {
		if (autoFetch && isContextReady) {
			fetchSessions();
		}
	}, [autoFetch, isContextReady, fetchSessions]);

	// Update filter data when selected employee changes
	useEffect(() => {
		if (selectedEmployee) {
			setFilterData(prev => ({
				...prev,
				employeeIds: selectedEmployee === 'all' ? null : [selectedEmployee]
			}));
		}
	}, [selectedEmployee]);

	return {
		filterData,
		setFilterData,
		sessions,
		loading,
		error,
		fetchSessions,
		clearError,
		resetFilter,
		isContextReady
	};
};

/**
 * Utility function to validate filter data
 */
export const validateFilterData = (filterData: ITrackingFilterData): { isValid: boolean; errors: string[] } => {
	const errors: string[] = [];

	// Validate date format
	const fromDate = new Date(filterData.from);
	const toDate = new Date(filterData.to);

	if (isNaN(fromDate.getTime())) {
		errors.push('Invalid from date format');
	}

	if (isNaN(toDate.getTime())) {
		errors.push('Invalid to date format');
	}

	// Validate date range
	if (!isNaN(fromDate.getTime()) && !isNaN(toDate.getTime()) && fromDate >= toDate) {
		errors.push('From date must be earlier than to date');
	}

	return {
		isValid: errors.length === 0,
		errors
	};
};

/**
 * Utility function to format filter data for display
 */
export const formatFilterSummary = (filterData: ITrackingFilterData): string => {
	const fromDate = new Date(filterData.from);
	const toDate = new Date(filterData.to);
	
	const dateOptions: Intl.DateTimeFormatOptions = {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
		hour: '2-digit',
		minute: '2-digit'
	};

	const fromFormatted = fromDate.toLocaleDateString('en-US', dateOptions);
	const toFormatted = toDate.toLocaleDateString('en-US', dateOptions);
	
	const employeeText = filterData.employeeIds 
		? `${filterData.employeeIds.length} employee(s)` 
		: 'All employees';

	return `${fromFormatted} - ${toFormatted} | ${employeeText}`;
};

export default useTrackingFilter;
