'use client';

import { useState, useEffect } from 'react';
import { cn } from '@cloc/ui';
import ClarityReplay, { formatDuration } from './clarity/clarity-replay';
import { Data, decode } from 'clarity-decode';
import { Building2, Users, Loader2 } from 'lucide-react';
import { ClocActiveEmployeeSelector, ClocActiveOrganizationSelector } from '@cloc/atoms';
import { TrackingFilter, useTrackingFilter } from './shared';
import { formatDateRange } from './utils';

export default function ClocTrackingSessionReplay({ className }: { className?: string }) {
	// Unified filter state management
	const { filterData, setFilterData, sessions, loading, error, fetchSessions } = useTrackingFilter({
		autoFetch: true,
		onError: (error) => {
			console.error('Session replay filter error:', error);
		}
	});

	// Session replay specific state
	const [currentSessionDecodedPayloads, setCurrentSessionDecodedPayloads] = useState<Data.DecodedPayload[]>([]);

	// Clear current session when sessions change (e.g., after applying new filters)
	useEffect(() => {
		// If the currently selected session is no longer in the sessions list, clear the selection
		if (currentSessionDecodedPayloads.length > 0) {
			const currentSessionId = currentSessionDecodedPayloads[0]?.envelope?.sessionId;
			const sessionExists = sessions.some((session) => session.sessionId === currentSessionId);

			if (!sessionExists) {
				setCurrentSessionDecodedPayloads([]);
			}
		}
	}, [sessions, currentSessionDecodedPayloads]);

	return (
		<div className={cn(' rounded-xl  shadow-lg bg-white dark:bg-black px-6 py-4 w-[90vw] h-fit ', className)}>
			<h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">Cloc Tracking Session Replay</h1>
			{/* Organization and Employee Selectors */}
			<div className="mb-4 rounded-lg border border-gray-200 dark:border-gray-700 p-3">
				<div className="flex flex-col lg:flex-row w-full gap-4">
					<div className="w-full">
						<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
							<Building2 className="inline h-4 w-4 mr-1" />
							Organization
						</label>
						<ClocActiveOrganizationSelector />
					</div>

					<div className="w-full">
						<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
							<Users className="inline h-4 w-4 mr-1" />
							Employee
						</label>
						<ClocActiveEmployeeSelector className="w-full" />
					</div>
				</div>
			</div>

			{/* Unified Filter Component */}
			<TrackingFilter
				filterData={filterData}
				onFilterChange={setFilterData}
				onApplyFilter={fetchSessions}
				loading={loading}
				defaultExpanded={true}
			/>

			{/* Error Display */}
			{error && (
				<div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
					<div className="text-red-800 dark:text-red-200 text-sm">
						<strong>Error:</strong> {error}
					</div>
				</div>
			)}

			{/* Results Summary */}
			<div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
				<div className="text-sm text-gray-600 dark:text-gray-400">
					<strong>Results:</strong>{' '}
					{loading ? 'Loading...' : `${sessions.length} session${sessions.length !== 1 ? 's' : ''} found`}
				</div>
			</div>

			<div className="flex gap-4 flex-col lg:flex-row">
				<div className=" flex-col dark:bg-black/70 bg-white text-black dark:text-white  flex gap-2 z-10 ">
					<div className="lg:w-72 w-full flex flex-col justify-center gap-4 rounded-lg border p-3 border-gray-200 dark:border-gray-700">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<h1 className="text-2xl font-bold text-gray-900 dark:text-white">Sessions</h1>
								{sessions.length > 0 && (
									<span className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full">
										{sessions.length}
									</span>
								)}
							</div>
							{currentSessionDecodedPayloads.length > 0 && (
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentSessionDecodedPayloads([])}
									className="text-xs px-2 py-1"
									title="Clear current session selection"
								>
									Clear
								</Button>
							)}
						</div>
						{sessions.map(({ sessionId, payloads, createdAt, updatedAt }) => {
							// Calculate total duration for this session

							const decodedPayloads = payloads.map(decode);

							const totalDuration = decodedPayloads.reduce(
								(sum, elt) => sum + (Number(elt.envelope.duration) || 0),
								0
							);

							return (
								<div
									key={sessionId}
									className={`flex flex-col gap-1 border text-xs rounded-lg border-gray-200 dark:border-gray-700 p-2 w-full cursor-pointer transition
                                    ${
										currentSessionDecodedPayloads?.[0]?.envelope?.sessionId ===
										decodedPayloads?.[0]?.envelope?.sessionId
											? 'bg-blue-100 dark:bg-blue-900 border-blue-400 dark:border-blue-500 ring-2 ring-blue-300 dark:ring-blue-700'
											: 'bg-gray-50 dark:bg-gray-900 hover:bg-blue-50 dark:hover:bg-blue-950'
									}`}
									onClick={() => setCurrentSessionDecodedPayloads(decodedPayloads)}
									title="Click to view this session"
								>
									<div className="flex justify-between font-semibold dark:text-blue-300">
										<div>
											Session ID: <span className="font-mono text-gray-500">{sessionId}</span>
										</div>

										<span className="text-gray-500 ">{formatDuration(totalDuration)}</span>
									</div>

									<div className="dark:text-blue-300">
										<span>Time range :</span>{' '}
										<span className="text-gray-500">{formatDateRange(createdAt, updatedAt)}</span>
									</div>

									<div className="dark:text-blue-300">
										<span>URL:</span>{' '}
										<span className="text-gray-500">
											{decodedPayloads[0] && decodedPayloads[0].envelope.url}
										</span>
									</div>
								</div>
							);
						})}

						{sessions.length === 0 && <div className="text-gray-500 text-sm">No sessions found.</div>}
					</div>
				</div>

				<div className="p-3  max-h-[720px]  w-full h-[80vh]  min-h-52 flex flex-col justify-center items-center rounded-lg border border-gray-200 dark:border-gray-700">
					{/* Loading State */}
					{loading && (
						<div className="flex flex-col items-center gap-3">
							<Loader2 className="animate-spin h-8 w-8 text-blue-500" />
							<p className="text-gray-500 text-sm">Loading sessions...</p>
						</div>
					)}

					{/* Error State */}
					{!loading && error && (
						<div className="flex flex-col items-center gap-3 text-center">
							<div className="text-red-500 text-lg">⚠️</div>
							<div>
								<p className="text-gray-700 dark:text-gray-300 font-medium">Error loading sessions</p>
								<p className="text-gray-500 text-sm mt-1">{error}</p>
							</div>
						</div>
					)}

					{/* No Sessions Available */}
					{!loading && !error && sessions.length === 0 && (
						<div className="flex flex-col items-center gap-3 text-center">
							<div className="text-gray-400 text-4xl">📹</div>
							<div>
								<p className="text-gray-700 dark:text-gray-300 font-medium">No sessions available</p>
								<p className="text-gray-500 text-sm mt-1">
									Try adjusting your filters or check back later for new sessions.
								</p>
							</div>
						</div>
					)}

					{/* Sessions Available but None Selected */}
					{!loading && !error && sessions.length > 0 && currentSessionDecodedPayloads.length === 0 && (
						<div className="flex flex-col items-center gap-3 text-center">
							<div className="text-blue-500 text-4xl">👆</div>
							<div>
								<p className="text-gray-700 dark:text-gray-300 font-medium">
									Please select a session to view the replay
								</p>
								<p className="text-gray-500 text-sm mt-1">
									Choose a session from the list on the left to start watching the replay.
								</p>
							</div>
						</div>
					)}

					{/* Session Selected - Show Replay or Heatmap */}
					{!loading && !error && sessions.length > 0 && currentSessionDecodedPayloads.length > 0 && (
						<div className="w-full h-full">
							<ClarityReplay decodedPayloads={currentSessionDecodedPayloads} />
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
