'use client';

import { useEffect } from 'react';
import { useState } from 'react';
import {
	Accordion,
	AccordionItem,
	AccordionTrigger,
	AccordionContent,
	Input,
	ThemedButton,
	DatePicker,
	Button,
	cn
} from '@cloc/ui';
import ClarityReplay, { formatDuration } from './clarity/clarity-replay';
import { Data, decode } from 'clarity-decode';
import { Building2, Calendar, ListFilter, Loader2, TimerIcon, Users, Info } from 'lucide-react';
import { ClocActiveEmployeeSelector, ClocActiveOrganizationSelector, useClocContext } from '@cloc/atoms';
import { IClocSession, isSuccessResponse } from './types/api-types';
import { trackingApiClient } from './api-client/tracking-client';
import { formatDateRange } from './utils';

export default function ClocTrackingSessionReplay({ className }: { className?: string }) {
	const [sessions, setSessions] = useState<IClocSession[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [currentSessionDecodedPayloads, setCurrentSessionDecodedPayloads] = useState<Data.DecodedPayload[]>([]);
	const { selectedOrganization, token, selectedEmployee, authenticatedUser: user } = useClocContext();

	const [formData, setFormData] = useState(() => {
		// Set from date to current time in user's timezone
		const fromDate = new Date();

		// Set to date to 1 hour later
		const toDate = new Date(fromDate.getTime() + 60 * 60 * 1000); // Add 1 hour

		return {
			from: fromDate.toISOString(),
			to: toDate.toISOString(),
			employeeIds: selectedEmployee === 'all' ? null : [selectedEmployee]
		};
	});

	const fetchSessions = async () => {
		setLoading(true);
		setError(null);

		try {
			if (!selectedOrganization || !selectedEmployee || !token || !user) {
				throw new Error('Missing organization, employee, or token');
			}

			// Validate date format
			const fromDate = new Date(formData.from);
			const toDate = new Date(formData.to);

			if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
				throw new Error('Invalid date format. Please use ISO 8601 format (e.g., 2024-01-01T00:00:00.000Z)');
			}

			if (fromDate >= toDate) {
				throw new Error('From date must be earlier than to date');
			}

			// Call the new filtered sessions endpoint
			const response = await trackingApiClient.getFilteredSessions(
				formData.from,
				formData.to,
				formData.employeeIds,
				selectedOrganization,
				user?.tenantId,
				token!
			);

			if (isSuccessResponse(response)) {
				setSessions(response.data);
				setError(null);
			} else {
				setError(response.error || 'Failed to fetch sessions');
				setSessions([]);
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : 'An unexpected error occurred');
			setSessions([]);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (selectedEmployee) {
			setFormData((prev) => ({
				...prev,
				employeeIds: selectedEmployee === 'all' ? null : [selectedEmployee]
			}));
		}
	}, [selectedEmployee]);

	useEffect(() => {
		// Only fetch sessions when all required context values are available
		if (selectedOrganization && selectedEmployee && token) {
			fetchSessions();
		}
	}, [selectedOrganization, selectedEmployee, token]);

	// Clear current session when sessions change (e.g., after applying new filters)
	useEffect(() => {
		// If the currently selected session is no longer in the sessions list, clear the selection
		if (currentSessionDecodedPayloads.length > 0) {
			const currentSessionId = currentSessionDecodedPayloads[0]?.envelope?.sessionId;
			const sessionExists = sessions.some((session) => session.sessionId === currentSessionId);

			if (!sessionExists) {
				setCurrentSessionDecodedPayloads([]);
			}
		}
	}, [sessions, currentSessionDecodedPayloads]);

	return (
		<div className={cn(' rounded-xl  shadow-lg bg-white dark:bg-black px-6 py-4 w-[90vw] h-fit ', className)}>
			<h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">Cloc Tracking Session Replay</h1>
			{/* Filter Information Display */}
			<div className="mb-4 rounded-lg border border-gray-200 dark:border-gray-700 p-3 ">
				<Accordion className=" w-full text-sm mb-4 rounded-lg " type="multiple">
					<AccordionItem value="filter">
						<AccordionTrigger className="py-0">
							<div className=" flex gap-2 justify-center items-center font-semibold text-gray-900 dark:text-white ">
								<ListFilter size={15} className="text-gray-400" />
								<h2>Filters</h2>
							</div>
						</AccordionTrigger>
						<AccordionContent className="p-0 w-full">
							<div className="flex flex-col lg:flex-row  w-full  gap-4 my-3">
								<div className="w-full">
									<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										<Building2 className="inline h-4 w-4 mr-1" />
										Organization
									</label>

									<ClocActiveOrganizationSelector />
								</div>

								<div className="w-full">
									<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										<Users className="inline h-4 w-4 mr-1" />
										Employee
									</label>

									<ClocActiveEmployeeSelector className="w-full" />
								</div>

								<div className="w-full">
									<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										<Calendar className="inline h-4 w-4 mr-1" />
										Date
									</label>

									<DatePicker
										date={new Date(formData.from.split('T')[0])}
										setDate={(newDate) => {
											if (!newDate) return;
											const date = newDate as Date;
											setFormData((prev) => ({
												...prev,
												from: `${date.toISOString().split('T')[0]}T${prev.from.split('T')[1]}`
											}));
										}}
										className=" w-full"
									/>
								</div>
								<div className="w-full">
									<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										<TimerIcon className="inline h-4 w-4 mr-1" />
										Time Range
									</label>

									<div className="flex w-full gap-2">
										<Input
											type="time"
											value={formData.from.split('T')[1].slice(0, 5)}
											onChange={(e) =>
												setFormData((prev) => ({
													...prev,
													from: new Date(
														`${prev.from.split('T')[0]}T${e.target.value}`
													).toISOString()
												}))
											}
											className="w-full"
										/>
										<Input
											type="time"
											value={formData.to.split('T')[1].slice(0, 5)}
											onChange={(e) =>
												setFormData((prev) => ({
													...prev,
													to: `${prev.to.split('T')[0]}T${e.target.value}:00.000Z`
												}))
											}
											className="w-full"
										/>
									</div>
								</div>
							</div>
							<ThemedButton onClick={fetchSessions} disabled={loading}>
								{loading && <Loader2 className="animate-spin h-4 w-4 mr-2" />} Apply filter
							</ThemedButton>
						</AccordionContent>
					</AccordionItem>
				</Accordion>

				{/* Results Summary */}
				<div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<Info size={14} className="text-gray-400" />
							<span className="text-xs text-gray-600 dark:text-gray-400">Results:</span>
						</div>
						<div className="flex items-center gap-2">
							{loading && <Loader2 className="animate-spin h-3 w-3 text-gray-400" />}
							<span className="text-xs font-semibold text-gray-900 dark:text-white" aria-live="polite">
								{loading
									? 'Loading...'
									: error
										? 'Error occurred'
										: `${sessions.length} session${sessions.length !== 1 ? 's' : ''} found`}
							</span>
						</div>
					</div>
					{error && (
						<div
							className="mt-2 text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded border border-red-200 dark:border-red-800"
							role="alert"
						>
							{error}
						</div>
					)}
				</div>
			</div>

			<div className="flex gap-4 flex-col lg:flex-row">
				<div className=" flex-col dark:bg-black/70 bg-white text-black dark:text-white  flex gap-2 z-10 ">
					<div className="lg:w-72 w-full flex flex-col justify-center gap-4 rounded-lg border p-3 border-gray-200 dark:border-gray-700">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<h1 className="text-2xl font-bold text-gray-900 dark:text-white">Sessions</h1>
								{sessions.length > 0 && (
									<span className="text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full">
										{sessions.length}
									</span>
								)}
							</div>
							{currentSessionDecodedPayloads.length > 0 && (
								<Button
									variant="outline"
									size="sm"
									onClick={() => setCurrentSessionDecodedPayloads([])}
									className="text-xs px-2 py-1"
									title="Clear current session selection"
								>
									Clear
								</Button>
							)}
						</div>
						{sessions.map(({ sessionId, payloads, createdAt, updatedAt }) => {
							// Calculate total duration for this session

							const decodedPayloads = payloads.map(decode);

							const totalDuration = decodedPayloads.reduce(
								(sum, elt) => sum + (Number(elt.envelope.duration) || 0),
								0
							);

							return (
								<div
									key={sessionId}
									className={`flex flex-col gap-1 border text-xs rounded-lg border-gray-200 dark:border-gray-700 p-2 w-full cursor-pointer transition
                                    ${
										currentSessionDecodedPayloads?.[0]?.envelope?.sessionId ===
										decodedPayloads?.[0]?.envelope?.sessionId
											? 'bg-blue-100 dark:bg-blue-900 border-blue-400 dark:border-blue-500 ring-2 ring-blue-300 dark:ring-blue-700'
											: 'bg-gray-50 dark:bg-gray-900 hover:bg-blue-50 dark:hover:bg-blue-950'
									}`}
									onClick={() => setCurrentSessionDecodedPayloads(decodedPayloads)}
									title="Click to view this session"
								>
									<div className="flex justify-between font-semibold dark:text-blue-300">
										<div>
											Session ID: <span className="font-mono text-gray-500">{sessionId}</span>
										</div>

										<span className="text-gray-500 ">{formatDuration(totalDuration)}</span>
									</div>

									<div className="dark:text-blue-300">
										<span>Time range :</span>{' '}
										<span className="text-gray-500">{formatDateRange(createdAt, updatedAt)}</span>
									</div>

									<div className="dark:text-blue-300">
										<span>URL:</span>{' '}
										<span className="text-gray-500">
											{decodedPayloads[0] && decodedPayloads[0].envelope.url}
										</span>
									</div>
								</div>
							);
						})}

						{sessions.length === 0 && <div className="text-gray-500 text-sm">No sessions found.</div>}
					</div>
				</div>

				<div className="p-3  max-h-[720px]  w-full h-[80vh]  min-h-52 flex flex-col justify-center items-center rounded-lg border border-gray-200 dark:border-gray-700">
					{/* Loading State */}
					{loading && (
						<div className="flex flex-col items-center gap-3">
							<Loader2 className="animate-spin h-8 w-8 text-blue-500" />
							<p className="text-gray-500 text-sm">Loading sessions...</p>
						</div>
					)}

					{/* Error State */}
					{!loading && error && (
						<div className="flex flex-col items-center gap-3 text-center">
							<div className="text-red-500 text-lg">⚠️</div>
							<div>
								<p className="text-gray-700 dark:text-gray-300 font-medium">Error loading sessions</p>
								<p className="text-gray-500 text-sm mt-1">{error}</p>
							</div>
						</div>
					)}

					{/* No Sessions Available */}
					{!loading && !error && sessions.length === 0 && (
						<div className="flex flex-col items-center gap-3 text-center">
							<div className="text-gray-400 text-4xl">📹</div>
							<div>
								<p className="text-gray-700 dark:text-gray-300 font-medium">No sessions available</p>
								<p className="text-gray-500 text-sm mt-1">
									Try adjusting your filters or check back later for new sessions.
								</p>
							</div>
						</div>
					)}

					{/* Sessions Available but None Selected */}
					{!loading && !error && sessions.length > 0 && currentSessionDecodedPayloads.length === 0 && (
						<div className="flex flex-col items-center gap-3 text-center">
							<div className="text-blue-500 text-4xl">👆</div>
							<div>
								<p className="text-gray-700 dark:text-gray-300 font-medium">
									Please select a session to view the replay
								</p>
								<p className="text-gray-500 text-sm mt-1">
									Choose a session from the list on the left to start watching the replay.
								</p>
							</div>
						</div>
					)}

					{/* Session Selected - Show Replay or Heatmap */}
					{!loading && !error && sessions.length > 0 && currentSessionDecodedPayloads.length > 0 && (
						<div className="w-full h-full">
							<ClarityReplay decodedPayloads={currentSessionDecodedPayloads} />
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
